{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "foreign_key": false, "primary_key": true, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "users_email_index", "nullable": false}, "name": ["atom", "email"], "type": ["atom", "string"], "source": ["atom", "email"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "users_last_name_first_name_index", "nullable": true}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": ["atom", "first_name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "users_last_name_first_name_index", "nullable": true}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": ["atom", "last_name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": ["atom", "age"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "index": true, "type": ["atom", "boolean"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "users_is_active_index", "nullable": true}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": ["atom", "is_active"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "jsonb"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "profile_data"], "type": ["atom", "map"], "source": ["atom", "profile_data"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": [], "index": false, "type": [["atom", "array"], ["atom", "string"]], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "tags"], "type": [["atom", "array"], ["atom", "string"]], "source": ["atom", "tags"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "float"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "score"], "type": ["atom", "float"], "source": ["atom", "score"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "date"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "birth_date"], "type": ["atom", "date"], "source": ["atom", "birth_date"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "last_login_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "last_login_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "users"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "foreign_key": false, "primary_key": true, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "users_email_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "users_email_index", "nullable": false}, "name": ["atom", "email"], "type": ["atom", "string"], "source": ["atom", "email"]}, "__struct__": "Field"}], "unique": true}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_is_active_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": true, "index": true, "type": ["atom", "boolean"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "users_is_active_index", "nullable": true}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": ["atom", "is_active"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_last_name_first_name_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "users_last_name_first_name_index", "nullable": true}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": ["atom", "first_name"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "users_last_name_first_name_index", "nullable": true}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": ["atom", "last_name"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}