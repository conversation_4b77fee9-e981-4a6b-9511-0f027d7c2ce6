{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "primary_key": true, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "version"], "type": ["atom", "id"], "source": ["atom", "version"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}], "source": ["atom", "schema_migrations"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "primary_key": true, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "version"], "type": ["atom", "id"], "source": ["atom", "version"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": {"attributes": {"indices": []}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}