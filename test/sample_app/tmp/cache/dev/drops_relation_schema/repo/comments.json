{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "foreign_key": false, "primary_key": true, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "body"], "type": ["atom", "string"], "source": ["atom", "body"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "boolean"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "comments_approved_index", "nullable": true}, "name": ["atom", "approved"], "type": ["atom", "boolean"], "source": ["atom", "approved"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "foreign_key": true, "primary_key": false, "check_constraints": [], "index_name": "comments_user_id_index", "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "id"], "source": ["atom", "user_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "foreign_key": true, "primary_key": false, "check_constraints": [], "index_name": "comments_post_id_index", "nullable": false}, "name": ["atom", "post_id"], "type": ["atom", "id"], "source": ["atom", "post_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "naive_datetime"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "naive_datetime"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "comments"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": ["atom", "auto_increment"], "index": false, "type": ["atom", "integer"], "foreign_key": false, "primary_key": true, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "id"], "type": ["atom", "id"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "post_id"], "references_field": ["atom", "id"], "references_table": ["atom", "posts"]}, "__struct__": "ForeignKey"}, {"attributes": {"field": ["atom", "user_id"], "references_field": ["atom", "id"], "references_table": ["atom", "users"]}, "__struct__": "ForeignKey"}], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "comments_approved_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "boolean"], "foreign_key": false, "primary_key": false, "check_constraints": [], "index_name": "comments_approved_index", "nullable": true}, "name": ["atom", "approved"], "type": ["atom", "boolean"], "source": ["atom", "approved"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "comments_post_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "foreign_key": true, "primary_key": false, "check_constraints": [], "index_name": "comments_post_id_index", "nullable": false}, "name": ["atom", "post_id"], "type": ["atom", "id"], "source": ["atom", "post_id"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "comments_user_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "foreign_key": true, "primary_key": false, "check_constraints": [], "index_name": "comments_user_id_index", "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "id"], "source": ["atom", "user_id"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}