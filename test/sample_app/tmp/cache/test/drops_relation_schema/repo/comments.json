{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "body"], "type": ["atom", "string"], "source": ["atom", "body"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "comments_approved_index"}, "name": ["atom", "approved"], "type": ["atom", "boolean"], "source": ["atom", "approved"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "comments_user_id_index"}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "comments_post_id_index"}, "name": ["atom", "post_id"], "type": ["atom", "integer"], "source": ["atom", "post_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "comments"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "post_id"], "references_table": ["atom", "posts"], "references_field": ["atom", "id"]}, "__struct__": "ForeignKey"}, {"attributes": {"field": ["atom", "user_id"], "references_table": ["atom", "users"], "references_field": ["atom", "id"]}, "__struct__": "ForeignKey"}], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "comments_approved_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "comments_approved_index"}, "name": ["atom", "approved"], "type": ["atom", "boolean"], "source": ["atom", "approved"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "comments_post_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "comments_post_id_index"}, "name": ["atom", "post_id"], "type": ["atom", "integer"], "source": ["atom", "post_id"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "comments_user_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "comments_user_id_index"}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}