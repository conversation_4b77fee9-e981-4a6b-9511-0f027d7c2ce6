{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "posts_title_index"}, "name": ["atom", "title"], "type": ["atom", "string"], "source": ["atom", "title"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "body"], "type": ["atom", "string"], "source": ["atom", "body"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "posts_published_index"}, "name": ["atom", "published"], "type": ["atom", "boolean"], "source": ["atom", "published"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 0, "index": false, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "view_count"], "type": ["atom", "integer"], "source": ["atom", "view_count"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "posts_user_id_index"}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "posts"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "user_id"], "references_table": ["atom", "users"], "references_field": ["atom", "id"]}, "__struct__": "ForeignKey"}], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "posts_title_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "posts_title_index"}, "name": ["atom", "title"], "type": ["atom", "string"], "source": ["atom", "title"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "posts_published_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": "posts_published_index"}, "name": ["atom", "published"], "type": ["atom", "boolean"], "source": ["atom", "published"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "posts_user_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "nullable": false, "check_constraints": [], "primary_key": false, "foreign_key": true, "index_name": "posts_user_id_index"}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}