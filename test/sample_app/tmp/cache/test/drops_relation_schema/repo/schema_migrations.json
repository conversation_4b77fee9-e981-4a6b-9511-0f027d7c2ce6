{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "version"], "type": ["atom", "integer"], "source": ["atom", "version"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "nullable": true, "check_constraints": [], "primary_key": false, "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}], "source": ["atom", "schema_migrations"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "nullable": true, "check_constraints": [], "primary_key": true, "foreign_key": false, "index_name": null}, "name": ["atom", "version"], "type": ["atom", "integer"], "source": ["atom", "version"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": {"attributes": {"indices": []}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}